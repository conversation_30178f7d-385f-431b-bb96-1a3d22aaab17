{"$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.16/MicrosoftTeams.schema.json", "manifestVersion": "1.16", "version": "1.0.0", "id": "com.accureMD.teamsbot", "packageName": "com.accureMD.teamsbot", "developer": {"name": "AccureMD", "websiteUrl": "https://accureMD.com", "privacyUrl": "https://accureMD.com/privacy", "termsOfUseUrl": "https://accureMD.com/terms"}, "icons": {"color": "color.png", "outline": "outline.png"}, "name": {"short": "AccureMD", "full": "AccureMD Meeting Assistant"}, "description": {"short": "AI-powered meeting recording and transcription assistant", "full": "AccureMD provides live transcription, meeting recording, and AI-powered insights for Microsoft Teams meetings. Join meetings as a guest, record audio and video, and get real-time transcripts."}, "accentColor": "#6264A7", "bots": [{"botId": "{{MICROSOFT_APP_ID}}", "scopes": ["personal", "team", "groupchat"], "supportsFiles": false, "isNotificationOnly": false, "supportsCalling": true, "supportsVideo": true}], "configurableTabs": [{"configurationUrl": "{{BASE_URL}}/configure.html", "canUpdateConfiguration": true, "scopes": ["team", "groupchat"], "context": ["meetingChatTab", "meetingSidePanel"]}], "staticTabs": [{"entityId": "accureMD-tab", "name": "AccureMD", "contentUrl": "{{BASE_URL}}/index.html", "websiteUrl": "{{BASE_URL}}/index.html", "scopes": ["personal"]}], "permissions": ["identity", "messageTeamMembers"], "validDomains": ["{{BASE_DOMAIN}}", "login.microsoftonline.com", "graph.microsoft.com"], "webApplicationInfo": {"id": "{{MICROSOFT_APP_ID}}", "resource": "https://graph.microsoft.com"}, "authorization": {"permissions": {"resourceSpecific": [{"name": "OnlineMeetings.ReadWrite.Chat", "type": "Application"}, {"name": "Calls.JoinGroupCall.Chat", "type": "Application"}]}}}