using Microsoft.AspNetCore.Mvc;
using AccureMD.TeamsBot.Services;
using AccureMD.TeamsBot.Models;

namespace AccureMD.TeamsBot.Controllers;

[Route("api/auth")]
[ApiController]
public class AuthController : ControllerBase
{
    private readonly AuthenticationService _authService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(AuthenticationService authService, ILogger<AuthController> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    [HttpGet("login")]
    public async Task<IActionResult> InitiateLogin([FromQuery] string userId, [FromQuery] string redirectUri)
    {
        try
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(redirectUri))
            {
                return BadRequest("UserId and redirectUri are required");
            }

            var result = await _authService.InitiateAuthenticationAsync(userId, redirectUri);

            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initiating authentication");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpPost("callback")]
    public async Task<IActionResult> AuthCallback([FromBody] AuthenticationRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Code))
            {
                return BadRequest("Authorization code is required");
            }

            var result = await _authService.HandleAuthenticationCallbackAsync(
                request.Code, 
                request.State, 
                request.RedirectUri);

            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling auth callback");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpGet("status/{userId}")]
    public async Task<IActionResult> GetAuthStatus(string userId)
    {
        try
        {
            var status = await _authService.GetAuthenticationStatusAsync(userId);
            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting auth status");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpPost("logout/{userId}")]
    public IActionResult Logout(string userId)
    {
        try
        {
            _authService.ClearUserSession(userId);
            return Ok(new { success = true, message = "Logged out successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
            return StatusCode(500, new { error = ex.Message });
        }
    }
}