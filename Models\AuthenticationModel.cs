using System.Text.Json.Serialization;

namespace AccureMD.TeamsBot.Models;

public class AuthenticationModel
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public DateTime TokenExpiry { get; set; }
    public bool IsAuthenticated { get; set; } = false;
    public List<string> Permissions { get; set; } = new();
}

public class AuthenticationRequest
{
    [JsonPropertyName("code")]
    public string Code { get; set; } = string.Empty;

    [JsonPropertyName("state")]
    public string State { get; set; } = string.Empty;

    [JsonPropertyName("redirectUri")]
    public string RedirectUri { get; set; } = string.Empty;
}

public class AuthenticationResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("user")]
    public AuthenticationModel? User { get; set; }

    [JsonPropertyName("authUrl")]
    public string AuthUrl { get; set; } = string.Empty;
}