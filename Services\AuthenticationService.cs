using Microsoft.Graph;
using Microsoft.Graph.Auth;
using Microsoft.Identity.Client;
using AccureMD.TeamsBot.Models;
using System.Text.Json;

namespace AccureMD.TeamsBot.Services;

public class AuthenticationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<AuthenticationService> _logger;
    private readonly Dictionary<string, AuthenticationModel> _userSessions;

    public AuthenticationService(IConfiguration configuration, ILogger<AuthenticationService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _userSessions = new Dictionary<string, AuthenticationModel>();
    }

    public async Task<AuthenticationResponse> InitiateAuthenticationAsync(string userId, string redirectUri)
    {
        try
        {
            var clientApp = ConfidentialClientApplicationBuilder
                .Create(_configuration["Teams:AppId"])
                .WithClientSecret(_configuration["Teams:AppSecret"])
                .WithAuthority($"https://login.microsoftonline.com/{_configuration["Teams:TenantId"]}")
                .Build();

            var scopes = new[]
            {
                "https://graph.microsoft.com/User.Read",
                "https://graph.microsoft.com/OnlineMeetings.ReadWrite",
                "https://graph.microsoft.com/Calendars.Read",
                "https://graph.microsoft.com/CallRecords.Read.All"
            };

            var authUrl = await clientApp
                .GetAuthorizationRequestUrl(scopes)
                .WithRedirectUri(redirectUri)
                .WithExtraQueryParameters($"state={userId}")
                .ExecuteAsync();

            _logger.LogInformation($"Generated auth URL for user {userId}");

            return new AuthenticationResponse
            {
                Success = true,
                Message = "Authentication URL generated",
                AuthUrl = authUrl.ToString()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to initiate authentication for user {userId}");
            return new AuthenticationResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
    }

    public async Task<AuthenticationResponse> HandleAuthenticationCallbackAsync(string code, string state, string redirectUri)
    {
        try
        {
            var clientApp = ConfidentialClientApplicationBuilder
                .Create(_configuration["Teams:AppId"])
                .WithClientSecret(_configuration["Teams:AppSecret"])
                .WithAuthority($"https://login.microsoftonline.com/{_configuration["Teams:TenantId"]}")
                .Build();

            var scopes = new[]
            {
                "https://graph.microsoft.com/User.Read",
                "https://graph.microsoft.com/OnlineMeetings.ReadWrite",
                "https://graph.microsoft.com/Calendars.Read",
                "https://graph.microsoft.com/CallRecords.Read.All"
            };

            var authResult = await clientApp
                .AcquireTokenByAuthorizationCode(scopes, code)
                .WithRedirectUri(redirectUri)
                .ExecuteAsync();

            // Create Graph client
            var graphClient = new GraphServiceClient(
                new DelegateAuthenticationProvider((requestMessage) =>
                {
                    requestMessage.Headers.Authorization = 
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", authResult.AccessToken);
                    return Task.FromResult(requestMessage);
                }));

            var user = await graphClient.Me.Request().GetAsync();

            var authModel = new AuthenticationModel
            {
                UserId = state, // This contains the original userId
                UserName = user.DisplayName ?? "",
                Email = user.Mail ?? user.UserPrincipalName ?? "",
                AccessToken = authResult.AccessToken,
                RefreshToken = authResult.Account.HomeAccountId.ToString(),
                TokenExpiry = authResult.ExpiresOn.DateTime,
                IsAuthenticated = true,
                Permissions = scopes.ToList()
            };

            // Store user session
            _userSessions[state] = authModel;

            _logger.LogInformation($"Successfully authenticated user {authModel.UserName}");

            return new AuthenticationResponse
            {
                Success = true,
                Message = "Authentication successful",
                User = authModel
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle authentication callback");
            return new AuthenticationResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
    }

    public async Task<AuthenticationResponse> VerifyAuthenticationAsync(string magicCode)
    {
        try
        {
            // This would typically verify against Microsoft Teams authentication
            // For now, we'll simulate a successful verification
            _logger.LogInformation($"Verifying magic code: {magicCode}");

            // In a real implementation, you would validate the magic code with Microsoft
            var isValid = !string.IsNullOrEmpty(magicCode);

            return new AuthenticationResponse
            {
                Success = isValid,
                Message = isValid ? "Verification successful" : "Invalid verification code"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify authentication");
            return new AuthenticationResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
    }

    public async Task<AuthenticationModel> GetAuthenticationStatusAsync(string userId)
    {
        await Task.CompletedTask; // For async pattern

        if (_userSessions.TryGetValue(userId, out var authModel))
        {
            // Check if token is still valid
            if (authModel.TokenExpiry > DateTime.UtcNow.AddMinutes(-5))
            {
                return authModel;
            }
            else
            {
                // Token expired, mark as not authenticated
                authModel.IsAuthenticated = false;
                return authModel;
            }
        }

        return new AuthenticationModel
        {
            UserId = userId,
            IsAuthenticated = false
        };
    }

    public async Task<bool> RefreshTokenAsync(string userId)
    {
        try
        {
            if (!_userSessions.TryGetValue(userId, out var authModel))
                return false;

            // Implement token refresh logic here
            // This would use the refresh token to get a new access token
            _logger.LogInformation($"Refreshing token for user {userId}");

            // Update token expiry
            authModel.TokenExpiry = DateTime.UtcNow.AddHours(1);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to refresh token for user {userId}");
            return false;
        }
    }

    public async Task<GraphServiceClient?> GetGraphClientForUserAsync(string userId)
    {
        var authModel = await GetAuthenticationStatusAsync(userId);

        if (!authModel.IsAuthenticated)
            return null;

        return new GraphServiceClient(
            new DelegateAuthenticationProvider((requestMessage) =>
            {
                requestMessage.Headers.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", authModel.AccessToken);
                return Task.FromResult(requestMessage);
            }));
    }

    public void ClearUserSession(string userId)
    {
        _userSessions.Remove(userId);
        _logger.LogInformation($"Cleared session for user {userId}");
    }
}