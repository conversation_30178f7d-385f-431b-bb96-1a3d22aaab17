# AccureMD Teams Bot Setup Script (PowerShell)

Write-Host "🚀 Setting up AccureMD Teams Bot..." -ForegroundColor Green

# Check prerequisites
Write-Host "📋 Checking prerequisites..." -ForegroundColor Yellow

if (-not (Get-Command "dotnet" -ErrorAction SilentlyContinue)) {
    Write-Host "❌ .NET 8 SDK not found. Please install .NET 8 SDK first." -ForegroundColor Red
    exit 1
}

if (-not (Get-Command "ngrok" -ErrorAction SilentlyContinue)) {
    Write-Host "⚠️  ngrok not found. Install ngrok for local development: npm install -g ngrok" -ForegroundColor Yellow
}

Write-Host "✅ Prerequisites check completed" -ForegroundColor Green

# Restore packages
Write-Host "📦 Restoring NuGet packages..." -ForegroundColor Yellow
dotnet restore

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ NuGet packages restored successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to restore NuGet packages" -ForegroundColor Red
    exit 1
}

# Build project
Write-Host "🔨 Building project..." -ForegroundColor Yellow
dotnet build

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Project built successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to build project" -ForegroundColor Red
    exit 1
}

# Create directories
Write-Host "📁 Creating required directories..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "recordings" | Out-Null
New-Item -ItemType Directory -Force -Path "storage/recordings" | Out-Null
New-Item -ItemType Directory -Force -Path "storage/transcripts" | Out-Null

# Setup configuration
Write-Host "⚙️  Setting up configuration..." -ForegroundColor Yellow
if (-not (Test-Path "appsettings.Production.json")) {
    Copy-Item "appsettings.json" "appsettings.Production.json"
    Write-Host "📝 Created appsettings.Production.json - Please update with production values" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "🎉 Setup completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Update appsettings.json with your Bot Framework and Azure credentials"
Write-Host "2. Update TeamsAppManifest/manifest.json with your Bot ID and domain"
Write-Host "3. Create Teams app icons (color.png 192x192, outline.png 32x32)"
Write-Host "4. Run 'dotnet run' to start the application"
Write-Host "5. Use ngrok for local development: 'ngrok http 5000'"
Write-Host "6. Update Bot Framework messaging endpoint with ngrok URL"
Write-Host "7. Install the Teams app in your tenant"
Write-Host ""
Write-Host "📚 See README.md for detailed setup instructions" -ForegroundColor Yellow
