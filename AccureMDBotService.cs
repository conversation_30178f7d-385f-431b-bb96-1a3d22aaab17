using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Teams;
using Microsoft.Bot.Schema;
using Microsoft.Bot.Schema.Teams;
using AccureMD.TeamsBot.Models;
using AccureMD.TeamsBot.Services;
using System.Text.Json;

namespace AccureMD.TeamsBot.Services;

public class AccureMDBotService : TeamsActivityHandler
{
    private readonly AuthenticationService _authService;
    private readonly MeetingService _meetingService;
    private readonly TranscriptionService _transcriptionService;
    private readonly ILogger<AccureMDBotService> _logger;

    public AccureMDBotService(
        AuthenticationService authService,
        MeetingService meetingService,
        TranscriptionService transcriptionService,
        ILogger<AccureMDBotService> logger)
    {
        _authService = authService;
        _meetingService = meetingService;
        _transcriptionService = transcriptionService;
        _logger = logger;
    }

    protected override async Task OnMessageActivityAsync(ITurnContext<IMessageActivity> turnContext, CancellationToken cancellationToken)
    {
        var userMessage = turnContext.Activity.Text?.Trim().ToLower();

        switch (userMessage)
        {
            case "hello":
            case "hi":
                await SendWelcomeMessage(turnContext, cancellationToken);
                break;

            case "help":
                await SendHelpMessage(turnContext, cancellationToken);
                break;

            default:
                await HandleUserCommand(turnContext, userMessage, cancellationToken);
                break;
        }
    }

    protected override async Task OnTeamsSigninVerifyStateAsync(ITurnContext<IInvokeActivity> turnContext, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Teams signin verify state invoked");

        var magicCode = turnContext.Activity.Value?.ToString();
        if (!string.IsNullOrEmpty(magicCode))
        {
            // Handle authentication verification
            var authResult = await _authService.VerifyAuthenticationAsync(magicCode);

            if (authResult.Success)
            {
                await turnContext.SendActivityAsync("✅ Successfully authenticated! You can now use AccureMD features.", cancellationToken: cancellationToken);
            }
            else
            {
                await turnContext.SendActivityAsync("❌ Authentication failed. Please try again.", cancellationToken: cancellationToken);
            }
        }
    }

    protected override async Task OnTeamsMembersAddedAsync(IList<TeamsChannelAccount> teamsMembersAdded, TeamInfo teamInfo, ITurnContext<IConversationUpdateActivity> turnContext, CancellationToken cancellationToken)
    {
        foreach (var member in teamsMembersAdded)
        {
            if (member.Id != turnContext.Activity.Recipient.Id)
            {
                await SendWelcomeMessage(turnContext, cancellationToken);
            }
        }
    }

    protected override async Task OnTeamsTabSubmitAsync(ITurnContext<IInvokeActivity> turnContext, TabSubmit tabSubmit, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Tab submit invoked: {JsonSerializer.Serialize(tabSubmit)}");

        var data = JsonSerializer.Deserialize<Dictionary<string, object>>(tabSubmit.Data.ToString() ?? "{}");

        if (data?.ContainsKey("action") == true)
        {
            var action = data["action"].ToString();

            switch (action)
            {
                case "joinMeeting":
                    await HandleJoinMeetingRequest(turnContext, data, cancellationToken);
                    break;

                case "startRecording":
                    await HandleStartRecording(turnContext, data, cancellationToken);
                    break;

                case "stopRecording":
                    await HandleStopRecording(turnContext, data, cancellationToken);
                    break;
            }
        }
    }

    private async Task SendWelcomeMessage(ITurnContext turnContext, CancellationToken cancellationToken)
    {
        var welcomeText = @"
🎉 **Welcome to AccureMD!**

AccureMD is your AI-powered meeting assistant that can:
• 📹 Record meeting video and audio
• 📝 Provide live transcription
• 🤖 Join meetings as a guest
• 💾 Save recordings and transcripts

To get started:
1. Click on **Apps** in your Teams meeting
2. Select **AccureMD**
3. Authenticate with your Microsoft account
4. Enter the meeting URL to join as a guest

Type **help** for more commands!";

        await turnContext.SendActivityAsync(MessageFactory.Text(welcomeText), cancellationToken);
    }

    private async Task SendHelpMessage(ITurnContext turnContext, CancellationToken cancellationToken)
    {
        var helpText = @"
🔧 **AccureMD Help**

**Available Commands:**
• `hello` - Welcome message
• `help` - Show this help message
• `status` - Check bot status
• `auth` - Authentication information

**Features:**
• **Meeting Recording** - High-quality audio/video recording
• **Live Transcription** - Real-time speech-to-text
• **Guest Access** - Join meetings without direct invitation
• **Secure Storage** - Encrypted recording and transcript storage

**Usage:**
1. Open AccureMD app in Teams meeting
2. Complete authentication
3. Enter meeting URL in sidebar
4. Start recording and transcription

Need more help? Contact <NAME_EMAIL>";

        await turnContext.SendActivityAsync(MessageFactory.Text(helpText), cancellationToken);
    }

    private async Task HandleUserCommand(ITurnContext turnContext, string? command, CancellationToken cancellationToken)
    {
        switch (command)
        {
            case "status":
                var status = await _meetingService.GetBotStatusAsync();
                await turnContext.SendActivityAsync($"🤖 AccureMD Status: {status}", cancellationToken: cancellationToken);
                break;

            case "auth":
                var authStatus = await _authService.GetAuthenticationStatusAsync(turnContext.Activity.From.Id);
                await turnContext.SendActivityAsync($"🔐 Authentication: {(authStatus.IsAuthenticated ? "✅ Authenticated" : "❌ Not authenticated")}", cancellationToken: cancellationToken);
                break;

            default:
                await turnContext.SendActivityAsync("I didn't understand that command. Type **help** for available commands.", cancellationToken: cancellationToken);
                break;
        }
    }

    private async Task HandleJoinMeetingRequest(ITurnContext turnContext, Dictionary<string, object> data, CancellationToken cancellationToken)
    {
        if (data.TryGetValue("meetingUrl", out var meetingUrlObj))
        {
            var meetingUrl = meetingUrlObj.ToString() ?? "";
            var userId = turnContext.Activity.From.Id;

            _logger.LogInformation($"Joining meeting: {meetingUrl} for user: {userId}");

            var joinResult = await _meetingService.JoinMeetingAsGuestAsync(meetingUrl, userId);

            if (joinResult.Success)
            {
                await turnContext.SendActivityAsync("🎯 Successfully joined the meeting as AccureMD Bot!", cancellationToken: cancellationToken);
            }
            else
            {
                await turnContext.SendActivityAsync($"❌ Failed to join meeting: {joinResult.Message}", cancellationToken: cancellationToken);
            }
        }
    }

    private async Task HandleStartRecording(ITurnContext turnContext, Dictionary<string, object> data, CancellationToken cancellationToken)
    {
        if (data.TryGetValue("meetingId", out var meetingIdObj))
        {
            var meetingId = meetingIdObj.ToString() ?? "";

            var recordingResult = await _meetingService.StartRecordingAsync(meetingId);

            if (recordingResult.Success)
            {
                await turnContext.SendActivityAsync("🔴 Recording started!", cancellationToken: cancellationToken);
            }
            else
            {
                await turnContext.SendActivityAsync($"❌ Failed to start recording: {recordingResult.Message}", cancellationToken: cancellationToken);
            }
        }
    }

    private async Task HandleStopRecording(ITurnContext turnContext, Dictionary<string, object> data, CancellationToken cancellationToken)
    {
        if (data.TryGetValue("meetingId", out var meetingIdObj))
        {
            var meetingId = meetingIdObj.ToString() ?? "";

            var stopResult = await _meetingService.StopRecordingAsync(meetingId);

            if (stopResult.Success)
            {
                await turnContext.SendActivityAsync("⏹️ Recording stopped and saved!", cancellationToken: cancellationToken);
            }
            else
            {
                await turnContext.SendActivityAsync($"❌ Failed to stop recording: {stopResult.Message}", cancellationToken: cancellationToken);
            }
        }
    }
}