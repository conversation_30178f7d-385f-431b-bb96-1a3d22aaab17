<!DOCTYPE html>
<html>
<head>
    <title>AccureMD - AI Meeting Assistant</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://res.cdn.office.net/teams-js/2.0.0/js/MicrosoftTeams.min.js"></script>
    <link rel="stylesheet" href="/css/teams-app.css">
</head>
<body class="main-app">
    <div id="loadingScreen" class="loading-screen">
        <div class="loader"></div>
        <p>Loading AccureMD...</p>
    </div>

    <div id="authScreen" class="auth-screen" style="display: none;">
        <div class="auth-container">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzYyNjRBNyIvPgo8cGF0aCBkPSJNMTYgMjRINDgiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iNCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxwYXRoIGQ9Ik0xNiAzNkg0OCIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSI0IiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHBhdGggZD0iTTE2IDQ4SDQ4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjQiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K" alt="AccureMD" class="logo-large">
            <h1>Welcome to AccureMD</h1>
            <p>Your AI-powered meeting assistant for Microsoft Teams</p>
            <button id="loginBtn" class="login-btn">
                <span class="ms-icon"></span>
                Sign in with Microsoft
            </button>
        </div>
    </div>

    <div id="mainApp" class="main-app-container" style="display: none;">
        <div class="header">
            <div class="user-info">
                <div class="avatar" id="userAvatar"></div>
                <span id="userName">User</span>
                <button id="logoutBtn" class="logout-btn">Sign Out</button>
            </div>
        </div>

        <div class="content">
            <div class="meeting-controls">
                <h2>🎯 Join Meeting</h2>
                <div class="input-group">
                    <label for="meetingUrl">Teams Meeting URL:</label>
                    <input type="url" id="meetingUrl" placeholder="https://teams.microsoft.com/l/meetup-join/..." />
                    <button id="joinBtn" class="primary-btn">Join as Guest</button>
                </div>

                <div class="status-display">
                    <div id="connectionStatus" class="status-item">
                        <span class="status-dot offline"></span>
                        <span>Not connected</span>
                    </div>
                </div>
            </div>

            <div class="recording-controls" id="recordingControls" style="display: none;">
                <h2>🔴 Recording Controls</h2>
                <div class="control-buttons">
                    <button id="startRecordingBtn" class="record-btn">Start Recording</button>
                    <button id="stopRecordingBtn" class="stop-btn" style="display: none;">Stop Recording</button>
                    <button id="startTranscriptionBtn" class="transcribe-btn">Start Transcription</button>
                </div>

                <div class="recording-status">
                    <div id="recordingTimer" class="timer">00:00:00</div>
                    <div id="recordingIndicator" class="recording-indicator">⚪ Standby</div>
                </div>
            </div>

            <div class="transcript-panel">
                <h2>📝 Live Transcript</h2>
                <div id="transcriptContainer" class="transcript-container">
                    <div class="no-transcript">
                        Start transcription to see live captions appear here...
                    </div>
                </div>
                <div class="transcript-controls">
                    <button id="clearTranscriptBtn" class="secondary-btn">Clear</button>
                    <button id="downloadTranscriptBtn" class="secondary-btn">Download</button>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>AccureMD v1.0 | AI Meeting Assistant</p>
        </div>
    </div>

    <script src="/js/teams-app.js"></script>
</body>
</html>