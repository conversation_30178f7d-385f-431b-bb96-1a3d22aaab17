<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyName>AccureMD.TeamsBot</AssemblyName>
    <RootNamespace>AccureMD.TeamsBot</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Bot.Builder" Version="4.21.2" />
    <PackageReference Include="Microsoft.Bot.Builder.Integration.AspNet.Core" Version="4.21.2" />
    <PackageReference Include="Microsoft.Bot.Builder.Teams" Version="4.20.0" />
    <PackageReference Include="Microsoft.Graph" Version="5.32.0" />
    <!-- Removed Microsoft.Graph.Authentication -->
    <PackageReference Include="Microsoft.Identity.Client" Version="4.54.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.18.0" />
    <PackageReference Include="Microsoft.CognitiveServices.Speech" Version="1.34.0" />
    <PackageReference Include="System.Text.Json" Version="7.0.2" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.0" />>
  </ItemGroup>

  <ItemGroup>
    <Content Include="TeamsAppManifest\**" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

</Project>