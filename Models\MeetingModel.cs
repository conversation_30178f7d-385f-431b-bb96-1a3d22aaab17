using System.Text.Json.Serialization;

namespace AccureMD.TeamsBot.Models;

public class MeetingModel
{
    public string Id { get; set; } = string.Empty;
    public string MeetingUrl { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string Status { get; set; } = "Pending"; // Pending, Active, Completed, Error
    public string OrganizerId { get; set; } = string.Empty;
    public List<string> Participants { get; set; } = new();
    public string RecordingPath { get; set; } = string.Empty;
    public string TranscriptPath { get; set; } = string.Empty;
    public bool IsRecording { get; set; } = false;
    public bool IsTranscribing { get; set; } = false;
}

public class MeetingJoinRequest
{
    [JsonPropertyName("meetingUrl")]
    public string MeetingUrl { get; set; } = string.Empty;

    [JsonPropertyName("userId")]
    public string UserId { get; set; } = string.Empty;

    [JsonPropertyName("displayName")]
    public string DisplayName { get; set; } = "AccureMD Bot";
}

public class MeetingResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("meetingId")]
    public string MeetingId { get; set; } = string.Empty;

    [JsonPropertyName("joinUrl")]
    public string JoinUrl { get; set; } = string.Empty;
}